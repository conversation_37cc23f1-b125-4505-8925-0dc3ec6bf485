/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables */
:root {
    --primary-color: #81cce6; /* Vibrant Light Blue */
    --secondary-color: #6db8d9; /* Darker Blue */
    --background-color: #000000; /* Black */
    --text-color: #ffffff; /* White */
    --text-secondary: #cccccc; /* Light gray */
    --border-color: #333333; /* Dark gray */
    --card-bg: #111111; /* Very dark gray */
    --font-family: 'Arial', 'Helvetica', sans-serif;
    --max-width: 1200px;
    --section-padding: 80px 0;
    --container-padding: 0 20px;
}

/* Base Typography */
body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: var(--container-padding);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3rem;
    font-weight: 900;
    letter-spacing: 2px;
}

h2 {
    font-size: 2.5rem;
    font-weight: 800;
    letter-spacing: 1px;
    text-align: center;
    margin-bottom: 3rem;
}

h3 {
    font-size: 1.5rem;
    font-weight: 700;
}

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--background-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-color);
    border-color: var(--text-color);
}

.btn-secondary:hover {
    background-color: var(--text-color);
    color: var(--background-color);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--background-color);
}

.btn-large {
    padding: 20px 40px;
    font-size: 1.2rem;
}

.btn-get-started {
    background-color: var(--primary-color);
    color: #000000;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-get-started:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Header and Navigation */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
    max-width: var(--max-width);
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    height: 60px;
    width: auto;
    object-fit: contain;
}

.logo-sa {
    background-color: var(--primary-color);
    color: var(--background-color);
    padding: 8px 12px;
    border-radius: 5px;
    font-weight: 900;
    font-size: 1.5rem;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 150px 0 var(--section-padding);
    text-align: center;
    background: linear-gradient(135deg, var(--background-color) 0%, #111111 100%);
}

.hero h1 {
    margin-bottom: 2rem;
    background: linear-gradient(45deg, var(--text-color), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    max-width: 800px;
    margin: 0 auto 3rem auto;
    color: var(--text-secondary);
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 4rem;
    flex-wrap: wrap;
}

.client-avatars {
    text-align: center;
}

.avatars {
    display: flex;
    justify-content: center;
    gap: -10px;
    margin-bottom: 1rem;
}

.avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid var(--background-color);
    margin-left: -10px;
    object-fit: cover;
    display: block;
}

.avatar:first-child {
    margin-left: 0;
}

/* Services Section */
.services {
    padding: var(--section-padding);
    background-color: var(--card-bg);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background-color: var(--background-color);
    padding: 2.5rem;
    border-radius: 10px;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, border-color 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.service-card h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.service-card p {
    margin-bottom: 2rem;
}

/* Benefits Section */
.benefits {
    padding: var(--section-padding);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.benefit-item {
    text-align: center;
    padding: 2rem;
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.benefit-item h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.case-study-preview {
    text-align: center;
    background-color: var(--card-bg);
    padding: 3rem;
    border-radius: 10px;
}

.case-study-chart h4 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 2rem;
}

.chart-placeholder {
    max-width: 600px;
    margin: 0 auto;
}

.chart-bars {
    display: flex;
    justify-content: space-around;
    align-items: end;
    height: 300px;
    gap: 1rem;
}

.bar {
    background: linear-gradient(to top, var(--secondary-color), var(--primary-color));
    border-radius: 5px 5px 0 0;
    position: relative;
    flex: 1;
    max-width: 80px;
    display: flex;
    align-items: end;
    justify-content: center;
}

.bar span {
    position: absolute;
    bottom: -50px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
    font-weight: 600;
}

/* Case Studies Section */
.case-studies {
    padding: var(--section-padding);
    background-color: var(--card-bg);
}

.case-study-detailed {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-bottom: 6rem;
    align-items: center;
}

.case-study-detailed:last-child {
    margin-bottom: 0;
}

.case-study-reverse {
    direction: rtl;
}

.case-study-reverse > * {
    direction: ltr;
}

.case-study-image {
    position: relative;
}

.dashboard-placeholder {
    background: linear-gradient(135deg, var(--background-color) 0%, #1a1a1a 100%);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    min-height: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.dashboard-placeholder:hover {
    transform: translateY(-5px);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

.dashboard-header h4 {
    color: var(--text-color);
    margin: 0;
    font-size: 1.2rem;
}

.dashboard-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.dashboard-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.metric-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.metric-title {
    display: block;
    color: var(--background-color);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.metric-amount {
    display: block;
    color: var(--background-color);
    font-size: 2rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.metric-change {
    display: block;
    color: var(--background-color);
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.9;
}

.chart-area {
    background: var(--background-color);
    border-radius: 8px;
    padding: 1.5rem;
    height: 120px;
    position: relative;
    overflow: hidden;
}

.chart-line {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px 4px 0 0;
    position: relative;
}

.chart-line::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    border-radius: 2px;
}

.dashboard-roas .dashboard-content {
    flex-direction: row;
    gap: 1rem;
}

.dashboard-roas .metric-card {
    flex: 1;
}

.case-study-content {
    padding: 2rem 0;
}

.case-study-content h3 {
    color: var(--text-color);
    font-size: 2.2rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    line-height: 1.1;
}

.case-study-content h4 {
    color: var(--primary-color);
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

.content-block {
    margin-bottom: 2rem;
}

.content-block h5 {
    color: var(--text-color);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.content-block p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 0;
}

.case-study-content .btn {
    margin-top: 1rem;
}

.case-study-img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    border: 5px solid #666666;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
    object-fit: cover;
    position: relative;
}

.case-study-img:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: var(--primary-color);
    box-shadow: 
        0 15px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px var(--primary-color),
        0 0 20px rgba(129, 204, 230, 0.4),
        0 0 40px rgba(129, 204, 230, 0.2),
        0 0 60px rgba(129, 204, 230, 0.1);
}

/* CTA Section */
.cta-section {
    padding: var(--section-padding);
    text-align: center;
    background: linear-gradient(135deg, var(--card-bg) 0%, var(--background-color) 100%);
}

.cta-section h2 {
    margin-bottom: 1.5rem;
}

.cta-section p {
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto 3rem auto;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* FAQ Section */
.faq {
    padding: var(--section-padding);
}

.faq-list {
    max-width: 800px;
    margin: 0 auto 3rem auto;
}

.faq-item {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    cursor: pointer;
    transition: color 0.3s ease;
}

.faq-question:hover {
    color: var(--primary-color);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.2rem;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: 300;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding: 0;
}

.faq-item.active .faq-answer {
    max-height: 200px;
    padding-bottom: 1.5rem;
}

.faq-cta {
    text-align: center;
}

/* Testimonials Section */
.testimonials {
    padding: var(--section-padding);
    background-color: var(--card-bg);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial {
    background-color: var(--background-color);
    padding: 2.5rem;
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.stars {
    color: #FFD700;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.testimonial p {
    font-style: italic;
    margin-bottom: 2rem;
    color: var(--text-color);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
}

.author-name {
    font-weight: 600;
    color: var(--text-color);
}

.author-company {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background-color: var(--background-color);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 1rem 0;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
    gap: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-logo-img {
    height: 53px;
    width: auto;
    object-fit: contain;
}

.footer-nav {
    display: flex;
    gap: 2rem;
}

.footer-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-nav a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    font-size: 1.5rem;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.footer-bottom p {
    color: var(--text-secondary);
    margin: 0;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    :root {
        --section-padding: 60px 0;
        --container-padding: 0 15px;
    }

    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--background-color);
        flex-direction: column;
        padding: 2rem 0;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        border-top: 1px solid var(--border-color);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 300px;
    }

    .services-grid,
    .benefits-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .case-study-detailed {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-bottom: 4rem;
    }

    .case-study-reverse {
        direction: ltr;
    }

    .case-study-content h3 {
        font-size: 1.8rem;
    }

    .case-study-content h4 {
        font-size: 1.2rem;
    }

    .dashboard-placeholder {
        min-height: 300px;
        padding: 1.5rem;
    }

    .dashboard-roas .dashboard-content {
        flex-direction: column;
        gap: 1rem;
    }

    .chart-bars {
        height: 200px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 2rem;
    }

    .service-card,
    .case-study,
    .testimonial {
        padding: 1.5rem;
    }

    .bar span {
        font-size: 0.8rem;
        bottom: -45px;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.service-card,
.benefit-item,
.case-study,
.testimonial {
    animation: fadeInUp 0.6s ease forwards;
}
